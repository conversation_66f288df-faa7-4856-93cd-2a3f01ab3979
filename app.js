const express = require('express');
const connectDB = require('./config/db');
const cors = require('cors');
const moment = require('moment-timezone');
const ExcelJS = require('exceljs');

// Import routes
const customerRoutes = require('./routes/customerRoutes');
const cylinderMasterRoutes = require('./routes/cylinderMasterRoutes');
const staffRoutes = require('./routes/StaffRoutes');
const factoryMasterRoutes = require('./routes/factoryRoute');
const truckRoutes = require('./routes/truckRoute');
const priceRoutes = require('./routes/priceMasterRoutes');
const roundMasterRoutes = require('./routes/RoundMasterRoutes');
const fillingProcessRoutes = require('./routes/FillingProcessRoutes');
const reportRoutes = require('./routes/ReportRoutes');
const lineNumberMasterRoutes = require('./routes/lineNumberMasterRoutes');
const inspectionRoutes = require('./routes/inspectionRoute');
const sellRoutes= require('./routes/SellRoutes');
const monitoringRoutes = require('./routes/monitoringRoutes');
const report = require('./routes/report');
const logRoutes = require('./routes/logRoutes');
const maintainRoutes = require('./routes/maintainRoute');
// Connect to MongoDB
connectDB();
const app = express();
app.use(express.json());
app.use(cors());


// Middleware
app.use('/api/customers', customerRoutes);
app.use('/api/cylinders', cylinderMasterRoutes);
app.use('/api/staff', staffRoutes);
app.use('/api/factories', factoryMasterRoutes);
app.use('/api/trucks', truckRoutes);
app.use('/api/prices', priceRoutes);
app.use('/api/rounds', roundMasterRoutes);
app.use('/api/filling-process', fillingProcessRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/line-numbers', lineNumberMasterRoutes);
app.use('/api/inspection', inspectionRoutes);
app.use('/api/sell', sellRoutes);
app.use('/api/monitoring',monitoringRoutes)
app.use('/api/logs', logRoutes);
app.use('/api', report);
app.use('/api/maintain', maintainRoutes);

// const staffcontroller = require('./controllers/StaffController');
// staffcontroller.createAdmin();

const t=moment().tz("Asia/Yangon").format();
console.log(t);
// Start server
const CylinderMaster = require('./models/CylinderMaster');

// app.get('/export-excel', async (req, res) => {
//   const cylinders = await CylinderMaster.find();

//   const workbook = new ExcelJS.Workbook();
//   const worksheet = workbook.addWorksheet('Data');

//   // Add headers
//   worksheet.columns = [
//     { header: 'Cylinder Size', key: 'cylinderSize', width: 15 },
//     { header: 'Import Date', key: 'importDate', width: 15 },
//     { header: 'Value Type', key: 'valueType', width: 15 },
//     { header: 'Production Date', key: 'productionDate', width: 15 },
//     { header: 'Original Number', key: 'originalNumber', width: 15 },
//     { header: 'Working Pressure', key: 'workingPressure', width: 15 },
//     { header: 'Design Pressure', key: 'designPressure', width: 15 },
//     { header: 'Status', key: 'status', width: 15 },
//     { header: 'Owned', key: 'owned', width: 10 },
//     { header: 'Serial Number', key: 'serialNumber', width: 20 },
//   ];

//    // Format data for Excel
//    const formattedData = cylinders.map((item) => ({
//     cylinderSize: item.cylinderSize,
//     importDate: item.importDate.toISOString().split('T')[0], // Format date
//     valueType: item.valueType,
//     productionDate: item.productionDate.toISOString().split('T')[0], // Format date
//     originalNumber: item.originalNumber,
//     workingPressure: item.workingPressure,
//     designPressure: item.designPressure,
//     status: item.status,
//     owned: item.owned ? 'Yes' : 'No',
//     serialNumber: item.serialNumber,
//   }));

//   // Add data rows
//   worksheet.addRows(formattedData);

//   // Send the Excel file
//   res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
//   res.setHeader('Content-Disposition', 'attachment; filename=cylinders.xlsx');
//   await workbook.xlsx.write(res);

//   res.end();
// });

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on  ${PORT}`);
});


