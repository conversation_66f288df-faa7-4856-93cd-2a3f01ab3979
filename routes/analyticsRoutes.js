const express = require('express');
const router = express.Router();
const AnalyticsController = require('../controllers/AnalyticsController');
const authMiddleware = require('../middleware/authMiddleware');

// Daily Analytics
router.get('/daily/:date', 
  authMiddleware(['root', 'admin', 'manager']), 
  AnalyticsController.getDailyAnalytics
);

// Monthly Analytics
router.get('/monthly/:date', 
  authMiddleware(['root', 'admin', 'manager']), 
  AnalyticsController.getMonthlyAnalytics
);

// Yearly Analytics
router.get('/yearly/:year', 
  authMiddleware(['root', 'admin', 'manager']), 
  AnalyticsController.getYearlyAnalytics
);

// Period Analytics (Custom Date Range)
router.get('/period', 
  authMiddleware(['root', 'admin', 'manager']), 
  AnalyticsController.getPeriodAnalytics
);

// Cylinder Return Analytics
router.get('/returns', 
  authMiddleware(['root', 'admin', 'manager']), 
  AnalyticsController.getCylinderReturnAnalytics
);

// Combined Sales and Returns Summary
router.get('/summary', 
  authMiddleware(['root', 'admin', 'manager']), 
  AnalyticsController.getCombinedSummary
);

module.exports = router;
