const SellMaster = require("../models/SellMaster");
const CylinderMaster = require("../models/CylinderMaster");
const CustomerMaster = require("../models/CustomerMaster");
const moment = require("moment-timezone");

// Helper function to get date range
const getDateRange = (period, startDate, endDate) => {
  const timezone = "Asia/Yangon";
  let start, end;

  switch (period) {
    case "daily":
      start = moment.tz(startDate, timezone).startOf("day").toDate();
      end = moment.tz(startDate, timezone).endOf("day").toDate();
      break;
    case "monthly":
      start = moment.tz(startDate, timezone).startOf("month").toDate();
      end = moment.tz(startDate, timezone).endOf("month").toDate();
      break;
    case "yearly":
      start = moment.tz(startDate, timezone).startOf("year").toDate();
      end = moment.tz(startDate, timezone).endOf("year").toDate();
      break;
    case "period":
      start = moment.tz(startDate, timezone).startOf("day").toDate();
      end = moment.tz(endDate, timezone).endOf("day").toDate();
      break;
    default:
      throw new Error("Invalid period type");
  }

  return { start, end };
};

// Helper function to calculate analytics
const calculateAnalytics = (sales) => {
  const analytics = {
    totalSales: 0,
    totalDiscount: 0,
    netSales: 0,
    totalQuantitySold: 0,
    totalQuantityReturned: 0,
    totalTransactions: sales.length,
    cashSales: {
      amount: 0,
      transactions: 0,
      quantity: 0,
    },
    creditSales: {
      amount: 0,
      transactions: 0,
      quantity: 0,
    },
    cylinderSizes: {},
    customerTypes: {},
    topCustomers: {},
    dailyBreakdown: {},
  };

  sales.forEach((sale) => {
    // Basic totals
    analytics.totalSales += sale.total;
    analytics.totalDiscount += sale.discount || 0;
    analytics.totalQuantitySold += sale.quantity;
    analytics.totalQuantityReturned += sale.returnedCylinders
      ? sale.returnedCylinders.length
      : 0;

    // Payment type analysis
    if (sale.payment === "Cash") {
      analytics.cashSales.amount += sale.total;
      analytics.cashSales.transactions += 1;
      analytics.cashSales.quantity += sale.quantity;
    } else if (sale.payment === "Credit") {
      analytics.creditSales.amount += sale.total;
      analytics.creditSales.transactions += 1;
      analytics.creditSales.quantity += sale.quantity;
    }

    // Cylinder size analysis
    if (sale.cylinders) {
      sale.cylinders.forEach((cyl) => {
        if (cyl.cylinder && cyl.cylinder.cylinderSize) {
          const size = cyl.cylinder.cylinderSize;
          if (!analytics.cylinderSizes[size]) {
            analytics.cylinderSizes[size] = { quantity: 0, amount: 0 };
          }
          analytics.cylinderSizes[size].quantity += 1;
          analytics.cylinderSizes[size].amount += cyl.price;
        }
      });
    }

    // Customer type analysis
    if (sale.customer && sale.customer.customerType) {
      const type = sale.customer.customerType;
      if (!analytics.customerTypes[type]) {
        analytics.customerTypes[type] = {
          transactions: 0,
          amount: 0,
          quantity: 0,
        };
      }
      analytics.customerTypes[type].transactions += 1;
      analytics.customerTypes[type].amount += sale.total;
      analytics.customerTypes[type].quantity += sale.quantity;
    }

    // Top customers analysis
    if (sale.customer) {
      const customerId = sale.customer._id.toString();
      const customerName = sale.customer.name;
      if (!analytics.topCustomers[customerId]) {
        analytics.topCustomers[customerId] = {
          name: customerName,
          transactions: 0,
          amount: 0,
          quantity: 0,
        };
      }
      analytics.topCustomers[customerId].transactions += 1;
      analytics.topCustomers[customerId].amount += sale.total;
      analytics.topCustomers[customerId].quantity += sale.quantity;
    }

    // Daily breakdown
    const day = moment(sale.createdAt).tz("Asia/Yangon").format("YYYY-MM-DD");
    if (!analytics.dailyBreakdown[day]) {
      analytics.dailyBreakdown[day] = {
        transactions: 0,
        amount: 0,
        quantity: 0,
        returns: 0,
        cashAmount: 0,
        creditAmount: 0,
      };
    }
    analytics.dailyBreakdown[day].transactions += 1;
    analytics.dailyBreakdown[day].amount += sale.total;
    analytics.dailyBreakdown[day].quantity += sale.quantity;
    analytics.dailyBreakdown[day].returns += sale.returnedCylinders
      ? sale.returnedCylinders.length
      : 0;

    if (sale.payment === "Cash") {
      analytics.dailyBreakdown[day].cashAmount += sale.total;
    } else if (sale.payment === "Credit") {
      analytics.dailyBreakdown[day].creditAmount += sale.total;
    }
  });

  analytics.netSales = analytics.totalSales - analytics.totalDiscount;

  // Convert top customers to array and sort
  analytics.topCustomers = Object.values(analytics.topCustomers)
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 10);

  return analytics;
};

// Get Daily Sales Analytics
exports.getDailyAnalytics = async (req, res) => {
  try {
    const { date } = req.params;

    if (!moment(date, "YYYY-MM-DD", true).isValid()) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM-DD" });
    }

    const { start, end } = getDateRange("daily", date);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    res.status(200).json({
      period: "daily",
      date: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      analytics,
      transactions: sales,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Monthly Sales Analytics
exports.getMonthlyAnalytics = async (req, res) => {
  try {
    const { date } = req.params; // Format: YYYY-MM

    if (!moment(date, "YYYY-MM", true).isValid()) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM" });
    }

    const { start, end } = getDateRange("monthly", date + "-01");

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    res.status(200).json({
      period: "monthly",
      month: moment(start).tz("Asia/Yangon").format("YYYY-MM"),
      analytics,
      transactionCount: sales.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Yearly Sales Analytics
exports.getYearlyAnalytics = async (req, res) => {
  try {
    const { year } = req.params;

    if (!moment(year, "YYYY", true).isValid()) {
      return res.status(400).json({ message: "Invalid year format. Use YYYY" });
    }

    const { start, end } = getDateRange("yearly", year + "-01-01");

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    // Monthly breakdown for the year
    const monthlyBreakdown = {};
    for (let month = 0; month < 12; month++) {
      const monthKey = moment().month(month).format("MMMM");
      monthlyBreakdown[monthKey] = {
        transactions: 0,
        amount: 0,
        quantity: 0,
        returns: 0,
        cashAmount: 0,
        creditAmount: 0,
      };
    }

    sales.forEach((sale) => {
      const monthKey = moment(sale.createdAt).format("MMMM");
      monthlyBreakdown[monthKey].transactions += 1;
      monthlyBreakdown[monthKey].amount += sale.total;
      monthlyBreakdown[monthKey].quantity += sale.quantity;
      monthlyBreakdown[monthKey].returns += sale.returnedCylinders
        ? sale.returnedCylinders.length
        : 0;

      if (sale.payment === "Cash") {
        monthlyBreakdown[monthKey].cashAmount += sale.total;
      } else if (sale.payment === "Credit") {
        monthlyBreakdown[monthKey].creditAmount += sale.total;
      }
    });

    res.status(200).json({
      period: "yearly",
      year: parseInt(year),
      analytics,
      monthlyBreakdown,
      transactionCount: sales.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Period Sales Analytics (Custom Date Range)
exports.getPeriodAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    if (
      !moment(startDate, "YYYY-MM-DD", true).isValid() ||
      !moment(endDate, "YYYY-MM-DD", true).isValid()
    ) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM-DD" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    res.status(200).json({
      period: "custom",
      startDate: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      endDate: moment(end).tz("Asia/Yangon").format("YYYY-MM-DD"),
      analytics,
      transactionCount: sales.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Cylinder Return Analytics
exports.getCylinderReturnAnalytics = async (req, res) => {
  try {
    const { period, startDate, endDate } = req.query;

    if (!period) {
      return res.status(400).json({
        message: "Period is required (daily, monthly, yearly, or period)",
      });
    }

    let dateRange;
    if (period === "period") {
      if (!startDate || !endDate) {
        return res.status(400).json({
          message:
            "Both startDate and endDate are required for period analytics",
        });
      }
      dateRange = getDateRange("period", startDate, endDate);
    } else {
      if (!startDate) {
        return res.status(400).json({ message: "startDate is required" });
      }
      dateRange = getDateRange(period, startDate);
    }

    const { start, end } = dateRange;

    // Get all sales with returned cylinders
    const salesWithReturns = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
      returnedCylinders: { $exists: true, $not: { $size: 0 } },
    })
      .populate("customer", "name customerType")
      .populate("returnedCylinders", "cylinderSize status")
      .populate("staff", "name");

    const returnAnalytics = {
      totalReturns: 0,
      returnsBySize: {},
      returnsByCustomerType: {},
      returnsByDay: {},
      topReturningCustomers: {},
    };

    salesWithReturns.forEach((sale) => {
      const returnCount = sale.returnedCylinders.length;
      returnAnalytics.totalReturns += returnCount;

      // Returns by cylinder size
      sale.returnedCylinders.forEach((cylinder) => {
        const size = cylinder.cylinderSize;
        if (!returnAnalytics.returnsBySize[size]) {
          returnAnalytics.returnsBySize[size] = 0;
        }
        returnAnalytics.returnsBySize[size] += 1;
      });

      // Returns by customer type
      if (sale.customer && sale.customer.customerType) {
        const type = sale.customer.customerType;
        if (!returnAnalytics.returnsByCustomerType[type]) {
          returnAnalytics.returnsByCustomerType[type] = 0;
        }
        returnAnalytics.returnsByCustomerType[type] += returnCount;
      }

      // Returns by day
      const day = moment(sale.createdAt).tz("Asia/Yangon").format("YYYY-MM-DD");
      if (!returnAnalytics.returnsByDay[day]) {
        returnAnalytics.returnsByDay[day] = 0;
      }
      returnAnalytics.returnsByDay[day] += returnCount;

      // Top returning customers
      if (sale.customer) {
        const customerId = sale.customer._id.toString();
        const customerName = sale.customer.name;
        if (!returnAnalytics.topReturningCustomers[customerId]) {
          returnAnalytics.topReturningCustomers[customerId] = {
            name: customerName,
            returns: 0,
          };
        }
        returnAnalytics.topReturningCustomers[customerId].returns +=
          returnCount;
      }
    });

    // Convert top returning customers to array and sort
    returnAnalytics.topReturningCustomers = Object.values(
      returnAnalytics.topReturningCustomers
    )
      .sort((a, b) => b.returns - a.returns)
      .slice(0, 10);

    res.status(200).json({
      period,
      startDate: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      endDate: moment(end).tz("Asia/Yangon").format("YYYY-MM-DD"),
      returnAnalytics,
      transactionsWithReturns: salesWithReturns.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Combined Sales and Returns Summary
exports.getCombinedSummary = async (req, res) => {
  try {
    const { period, startDate, endDate } = req.query;

    if (!period) {
      return res.status(400).json({
        message: "Period is required (daily, monthly, yearly, or period)",
      });
    }

    let dateRange;
    if (period === "period") {
      if (!startDate || !endDate) {
        return res.status(400).json({
          message:
            "Both startDate and endDate are required for period analytics",
        });
      }
      dateRange = getDateRange("period", startDate, endDate);
    } else {
      if (!startDate) {
        return res.status(400).json({ message: "startDate is required" });
      }
      dateRange = getDateRange(period, startDate);
    }

    const { start, end } = dateRange;

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    // Additional summary metrics
    const summary = {
      ...analytics,
      averageTransactionValue:
        analytics.totalTransactions > 0
          ? analytics.totalSales / analytics.totalTransactions
          : 0,
      returnRate:
        analytics.totalQuantitySold > 0
          ? (analytics.totalQuantityReturned / analytics.totalQuantitySold) *
            100
          : 0,
      cashPercentage:
        analytics.totalSales > 0
          ? (analytics.cashSales.amount / analytics.totalSales) * 100
          : 0,
      creditPercentage:
        analytics.totalSales > 0
          ? (analytics.creditSales.amount / analytics.totalSales) * 100
          : 0,
    };

    res.status(200).json({
      period,
      startDate: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      endDate: moment(end).tz("Asia/Yangon").format("YYYY-MM-DD"),
      summary,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Daily Overview Report (Tabular format like your example)
exports.getDailyOverview = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    if (
      !moment(startDate, "YYYY-MM-DD", true).isValid() ||
      !moment(endDate, "YYYY-MM-DD", true).isValid()
    ) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM-DD" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    // Get all sales data for the period
    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Get total cylinders count from CylinderMaster
    const totalCylinders = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
    });

    // Create daily data structure
    const dailyData = {};
    const dateHeaders = [];

    // Generate all dates in the range
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      const dateKey = currentDate.format("YYYY-MM-DD");
      const dayNumber = currentDate.format("D");
      const monthYear = currentDate.format("MMM-YY");

      dateHeaders.push({
        date: dateKey,
        day: dayNumber,
        monthYear: monthYear,
        fullDate: currentDate.format("DD MMM YYYY"),
      });

      dailyData[dateKey] = {
        deliver: 0,
        return: 0,
        balanceInCirculation: 0,
        balanceAtFactory: 0,
        totalCylinders: totalCylinders,
      };

      currentDate.add(1, "day");
    }

    // Process sales data
    sales.forEach((sale) => {
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (dailyData[saleDate]) {
        // Count delivered cylinders
        dailyData[saleDate].deliver += sale.quantity;

        // Count returned cylinders
        if (sale.returnedCylinders) {
          dailyData[saleDate].return += sale.returnedCylinders.length;
        }
      }
    });

    // Calculate running balances
    let runningCirculation = 0;

    // Get initial circulation count (cylinders that are 'Taken' before start date)
    const initialCirculation = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
      status: "Taken",
    });

    runningCirculation = initialCirculation;

    // Calculate balances for each day
    Object.keys(dailyData)
      .sort()
      .forEach((dateKey) => {
        const dayData = dailyData[dateKey];

        // Update circulation: add delivered, subtract returned
        runningCirculation =
          runningCirculation + dayData.deliver - dayData.return;

        dayData.balanceInCirculation = runningCirculation;
        dayData.balanceAtFactory = totalCylinders - runningCirculation;
      });

    // Format response similar to your table structure
    const overview = {
      period: {
        from: moment(start).tz("Asia/Yangon").format("Do MMMM"),
        to: moment(end).tz("Asia/Yangon").format("Do MMMM"),
        fromDate: startDate,
        toDate: endDate,
      },
      headers: dateHeaders,
      dailyOverall: {
        deliver: dateHeaders.map((header) => dailyData[header.date].deliver),
        return: dateHeaders.map((header) => dailyData[header.date].return),
        balanceInCirculation: dateHeaders.map(
          (header) => dailyData[header.date].balanceInCirculation
        ),
        balanceAtFactory: dateHeaders.map(
          (header) => dailyData[header.date].balanceAtFactory
        ),
        totalCylinders: dateHeaders.map(
          (header) => dailyData[header.date].totalCylinders
        ),
      },
      dailyData: dailyData,
      summary: {
        totalDelivered: Object.values(dailyData).reduce(
          (sum, day) => sum + day.deliver,
          0
        ),
        totalReturned: Object.values(dailyData).reduce(
          (sum, day) => sum + day.return,
          0
        ),
        netDelivered: Object.values(dailyData).reduce(
          (sum, day) => sum + day.deliver - day.return,
          0
        ),
        totalCylinders: totalCylinders,
      },
    };

    res.status(200).json(overview);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Daily Overview by Customer
exports.getDailyOverviewByCustomer = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Generate date headers
    const dateHeaders = [];
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      dateHeaders.push({
        date: currentDate.format("YYYY-MM-DD"),
        day: currentDate.format("D"),
        monthYear: currentDate.format("MMM-YY"),
      });
      currentDate.add(1, "day");
    }

    // Group by customer
    const customerData = {};

    sales.forEach((sale) => {
      if (!sale.customer) return;

      const customerId = sale.customer._id.toString();
      const customerName = sale.customer.name;
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (!customerData[customerId]) {
        customerData[customerId] = {
          name: customerName,
          customerType: sale.customer.customerType,
          dailyDelivery: {},
          dailyReturn: {},
          totalDelivered: 0,
          totalReturned: 0,
        };

        // Initialize all dates with 0
        dateHeaders.forEach((header) => {
          customerData[customerId].dailyDelivery[header.date] = 0;
          customerData[customerId].dailyReturn[header.date] = 0;
        });
      }

      // Add delivery data
      customerData[customerId].dailyDelivery[saleDate] += sale.quantity;
      customerData[customerId].totalDelivered += sale.quantity;

      // Add return data
      if (sale.returnedCylinders) {
        customerData[customerId].dailyReturn[saleDate] +=
          sale.returnedCylinders.length;
        customerData[customerId].totalReturned += sale.returnedCylinders.length;
      }
    });

    // Format for table display
    const customerOverview = Object.values(customerData).map((customer) => ({
      customerName: customer.name,
      customerType: customer.customerType,
      deliveryByDay: dateHeaders.map(
        (header) => customer.dailyDelivery[header.date]
      ),
      returnByDay: dateHeaders.map(
        (header) => customer.dailyReturn[header.date]
      ),
      totalDelivered: customer.totalDelivered,
      totalReturned: customer.totalReturned,
      netDelivered: customer.totalDelivered - customer.totalReturned,
    }));

    res.status(200).json({
      period: {
        from: moment(start).tz("Asia/Yangon").format("Do MMMM"),
        to: moment(end).tz("Asia/Yangon").format("Do MMMM"),
        fromDate: startDate,
        toDate: endDate,
      },
      headers: dateHeaders,
      customerOverview: customerOverview.sort(
        (a, b) => b.totalDelivered - a.totalDelivered
      ),
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Daily Overview by Customer Type
exports.getDailyOverviewByCustomerType = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Generate date headers
    const dateHeaders = [];
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      dateHeaders.push({
        date: currentDate.format("YYYY-MM-DD"),
        day: currentDate.format("D"),
        monthYear: currentDate.format("MMM-YY"),
      });
      currentDate.add(1, "day");
    }

    // Group by customer type
    const customerTypeData = {};
    const customerTypes = [
      "Hospital",
      "Individual",
      "Shop",
      "Factory",
      "Workshop",
    ];

    // Initialize customer types
    customerTypes.forEach((type) => {
      customerTypeData[type] = {
        dailyDelivery: {},
        dailyReturn: {},
        totalDelivered: 0,
        totalReturned: 0,
      };

      dateHeaders.forEach((header) => {
        customerTypeData[type].dailyDelivery[header.date] = 0;
        customerTypeData[type].dailyReturn[header.date] = 0;
      });
    });

    sales.forEach((sale) => {
      if (!sale.customer || !sale.customer.customerType) return;

      const customerType = sale.customer.customerType;
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (customerTypeData[customerType]) {
        // Add delivery data
        customerTypeData[customerType].dailyDelivery[saleDate] += sale.quantity;
        customerTypeData[customerType].totalDelivered += sale.quantity;

        // Add return data
        if (sale.returnedCylinders) {
          customerTypeData[customerType].dailyReturn[saleDate] +=
            sale.returnedCylinders.length;
          customerTypeData[customerType].totalReturned +=
            sale.returnedCylinders.length;
        }
      }
    });

    // Format for table display
    const customerTypeOverview = Object.keys(customerTypeData)
      .map((type) => ({
        customerType: type,
        deliveryByDay: dateHeaders.map(
          (header) => customerTypeData[type].dailyDelivery[header.date]
        ),
        returnByDay: dateHeaders.map(
          (header) => customerTypeData[type].dailyReturn[header.date]
        ),
        totalDelivered: customerTypeData[type].totalDelivered,
        totalReturned: customerTypeData[type].totalReturned,
        netDelivered:
          customerTypeData[type].totalDelivered -
          customerTypeData[type].totalReturned,
      }))
      .filter((type) => type.totalDelivered > 0); // Only show types with activity

    res.status(200).json({
      period: {
        from: moment(start).tz("Asia/Yangon").format("Do MMMM"),
        to: moment(end).tz("Asia/Yangon").format("Do MMMM"),
        fromDate: startDate,
        toDate: endDate,
      },
      headers: dateHeaders,
      customerTypeOverview: customerTypeOverview.sort(
        (a, b) => b.totalDelivered - a.totalDelivered
      ),
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
